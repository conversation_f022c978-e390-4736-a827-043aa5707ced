'use client';

import React, { useState } from 'react';

interface SidebarProps {
  activeIcon?: string;
}

const Sidebar: React.FC<SidebarProps> = ({ activeIcon = 'home' }) => {
  const [hoveredIcon, setHoveredIcon] = useState<string | null>(null);

  const navigationIcons = [
    { id: 'home', icon: '🏠', label: 'Home' },
    { id: 'ai', icon: '✨', label: 'AI Assistant' },
    { id: 'chart', icon: '📊', label: 'Charts' },
    { id: 'data', icon: '💾', label: 'Data' },
    { id: 'brain', icon: '🧠', label: 'ML Models' },
  ];

  const IconButton = ({ 
    id, 
    icon, 
    label, 
    isActive, 
    isHovered 
  }: { 
    id: string; 
    icon: string; 
    label: string; 
    isActive: boolean; 
    isHovered: boolean; 
  }) => (
    <button
      className={`
        w-12 h-12 flex items-center justify-center rounded-lg
        transition-all duration-200 ease-in-out
        ${isActive 
          ? 'bg-white bg-opacity-20 scale-105' 
          : isHovered 
            ? 'bg-white bg-opacity-10 scale-105' 
            : 'hover:bg-white hover:bg-opacity-10 hover:scale-105'
        }
      `}
      onMouseEnter={() => setHoveredIcon(id)}
      onMouseLeave={() => setHoveredIcon(null)}
      aria-label={label}
    >
      <span className="text-white text-xl">{icon}</span>
    </button>
  );

  return (
    <div className="fixed left-0 top-0 h-full w-20 bg-primary flex flex-col items-center py-6 z-50">
      {/* Menu Icon */}
      <button 
        className="w-12 h-12 flex items-center justify-center rounded-lg mb-8 hover:bg-white hover:bg-opacity-10 transition-all duration-200"
        aria-label="Menu"
      >
        <div className="flex flex-col space-y-1">
          <div className="w-5 h-0.5 bg-white"></div>
          <div className="w-5 h-0.5 bg-white"></div>
          <div className="w-5 h-0.5 bg-white"></div>
        </div>
      </button>

      {/* Navigation Icons */}
      <div className="flex flex-col space-y-4 flex-1">
        {navigationIcons.map((item) => (
          <IconButton
            key={item.id}
            id={item.id}
            icon={item.icon}
            label={item.label}
            isActive={activeIcon === item.id}
            isHovered={hoveredIcon === item.id}
          />
        ))}
      </div>

      {/* Settings Icon */}
      <button 
        className="w-12 h-12 flex items-center justify-center rounded-lg hover:bg-white hover:bg-opacity-10 transition-all duration-200"
        aria-label="Settings"
      >
        <span className="text-white text-xl">⚙️</span>
      </button>
    </div>
  );
};

export default Sidebar;
