'use client';

import React from 'react';
import HistoryPanel from './HistoryPanel';

interface MainContentProps {
  userName?: string;
}

const MainContent: React.FC<MainContentProps> = ({ userName = "Ram" }) => {
  return (
    <div className="ml-20 min-h-screen bg-background flex flex-col">
      {/* Top Bar */}
      <div className="flex justify-between items-center p-8">
        {/* Brand Name */}
        <h1 className="text-text-dark font-bold text-4xl tracking-wide">
          DIORITE
        </h1>

        {/* User Avatar */}
        <div className="w-12 h-12 bg-primary rounded-full flex items-center justify-center">
          <span className="text-white font-semibold text-lg">
            {userName.charAt(0).toUpperCase()}
          </span>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="flex-1 flex items-center justify-between px-8 pb-8">
        {/* Welcome Message & CTA */}
        <div className="flex-1 max-w-2xl">
          {/* Greeting */}
          <h2 className="text-primary font-semibold text-5xl mb-4">
            Hello {userName},
          </h2>

          {/* Instructional Text */}
          <p className="text-text-dark text-xl">
            Upload Dataset to{' '}
            <span className="text-primary font-semibold">Start</span>
          </p>

          {/* Upload Area (Optional - can be added later) */}
          <div className="mt-12">
            <div className="border-2 border-dashed border-primary border-opacity-30 rounded-2xl p-12 text-center hover:border-opacity-50 transition-all duration-200 cursor-pointer">
              <div className="text-primary text-6xl mb-4">📁</div>
              <p className="text-text-dark text-lg mb-2">
                Drag and drop your dataset here
              </p>
              <p className="text-text-dark text-sm opacity-60">
                or click to browse files
              </p>
            </div>
          </div>
        </div>

        {/* History Panel */}
        <div className="ml-8">
          <HistoryPanel />
        </div>
      </div>
    </div>
  );
};

export default MainContent;
