'use client';

import React, { useState } from 'react';

interface HistoryItem {
  id: string;
  filename: string;
  uploadDate?: string;
}

interface HistoryPanelProps {
  items?: HistoryItem[];
}

const HistoryPanel: React.FC<HistoryPanelProps> = ({ 
  items = [
    { id: '1', filename: 'steps dataset.csv' },
    { id: '2', filename: 'nlp.csv' },
    { id: '3', filename: 'calorie tracking.csv' },
  ]
}) => {
  const [hoveredItem, setHoveredItem] = useState<string | null>(null);

  return (
    <div className="bg-primary rounded-2xl p-6 w-80 h-96 flex flex-col">
      {/* Title */}
      <h2 className="text-white font-bold text-xl mb-6 tracking-wide">
        HISTORY
      </h2>

      {/* Dataset List */}
      <div className="flex-1 overflow-y-auto">
        <div className="space-y-3">
          {items.map((item) => (
            <button
              key={item.id}
              className={`
                w-full text-left p-3 rounded-lg transition-all duration-200
                ${hoveredItem === item.id 
                  ? 'bg-white bg-opacity-15 transform translate-x-1' 
                  : 'hover:bg-white hover:bg-opacity-10 hover:transform hover:translate-x-1'
                }
              `}
              onMouseEnter={() => setHoveredItem(item.id)}
              onMouseLeave={() => setHoveredItem(null)}
            >
              <div className="flex items-center space-x-3">
                {/* File Icon */}
                <div className="flex-shrink-0">
                  <span className="text-white text-lg">📄</span>
                </div>
                
                {/* Filename */}
                <span className="text-white text-sm font-medium truncate">
                  {item.filename}
                </span>
              </div>
            </button>
          ))}
        </div>
      </div>

      {/* Empty State */}
      {items.length === 0 && (
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <span className="text-white text-4xl mb-4 block">📁</span>
            <p className="text-white text-sm opacity-75">
              No datasets uploaded yet
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

export default HistoryPanel;
