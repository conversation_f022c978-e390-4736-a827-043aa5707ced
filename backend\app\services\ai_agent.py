"""
AI Agent service using <PERSON><PERSON><PERSON><PERSON> for data science tasks.
"""
from typing import Dict, List, Any, Optional
from langchain.agents import Agent<PERSON>xecutor, create_react_agent
from langchain.tools import Tool
from langchain_openai import ChatOpenAI
from langchain.prompts import PromptTemplate
from langchain.memory import ConversationBufferMemory
import pandas as pd
import json

from app.core.config import settings
from app.services.data_processor import data_processor
from app.services.visualization_service import visualization_service
from app.services.ml_service import ml_service
import structlog

logger = structlog.get_logger()


class DataScienceAgent:
    """AI Agent for data science tasks."""
    
    def __init__(self):
        self.llm = ChatOpenAI(
            model="gpt-4",
            temperature=0.1,
            openai_api_key=settings.OPENAI_API_KEY
        )
        self.tools = self._create_tools()
        self.agent = self._create_agent()
    
    def _create_tools(self) -> List[Tool]:
        """Create tools for the agent."""
        return [
            Tool(
                name="load_dataset",
                description="Load a dataset by ID. Input: dataset_id (int)",
                func=self._load_dataset_tool
            ),
            Tool(
                name="analyze_dataset",
                description="Analyze dataset and get statistics. Input: dataset_id (int)",
                func=self._analyze_dataset_tool
            ),
            Tool(
                name="create_visualization",
                description="Create a visualization. Input: JSON with dataset_id, chart_type, columns",
                func=self._create_visualization_tool
            ),
            Tool(
                name="train_model",
                description="Train a machine learning model. Input: JSON with dataset_id, model_type, target_column",
                func=self._train_model_tool
            ),
            Tool(
                name="execute_code",
                description="Execute Python code safely. Input: Python code as string",
                func=self._execute_code_tool
            ),
            Tool(
                name="clean_data",
                description="Clean dataset with specified options. Input: JSON with dataset_id and cleaning options",
                func=self._clean_data_tool
            ),
            Tool(
                name="get_column_info",
                description="Get information about dataset columns. Input: dataset_id (int)",
                func=self._get_column_info_tool
            )
        ]
    
    def _create_agent(self) -> AgentExecutor:
        """Create the ReAct agent."""
        prompt = PromptTemplate.from_template("""
You are a helpful AI data science assistant. You can help users analyze data, create visualizations, 
train machine learning models, and execute Python code safely.

Available tools:
{tools}

Use the following format:

Question: the input question you must answer
Thought: you should always think about what to do
Action: the action to take, should be one of [{tool_names}]
Action Input: the input to the action
Observation: the result of the action
... (this Thought/Action/Action Input/Observation can repeat N times)
Thought: I now know the final answer
Final Answer: the final answer to the original input question

Question: {input}
Thought: {agent_scratchpad}
""")
        
        agent = create_react_agent(
            llm=self.llm,
            tools=self.tools,
            prompt=prompt
        )
        
        return AgentExecutor(
            agent=agent,
            tools=self.tools,
            verbose=True,
            max_iterations=10,
            handle_parsing_errors=True
        )
    
    def process_message(
        self, 
        message: str, 
        user_id: int, 
        session_context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Process a user message and return response."""
        try:
            # Add user context to the message
            context_info = ""
            if session_context:
                if "current_dataset_id" in session_context:
                    context_info += f"Current dataset ID: {session_context['current_dataset_id']}\n"
                if "available_datasets" in session_context:
                    context_info += f"Available datasets: {session_context['available_datasets']}\n"
            
            enhanced_message = f"{context_info}\nUser question: {message}"
            
            # Execute agent
            result = self.agent.invoke({"input": enhanced_message})
            
            return {
                "response": result["output"],
                "intermediate_steps": result.get("intermediate_steps", []),
                "success": True
            }
            
        except Exception as e:
            logger.error("Agent processing failed", error=str(e), user_id=user_id)
            return {
                "response": f"I encountered an error: {str(e)}",
                "success": False,
                "error": str(e)
            }
    
    def _load_dataset_tool(self, dataset_id: str) -> str:
        """Tool to load dataset information."""
        try:
            from app.core.database import SessionLocal
            from app.models.dataset import Dataset
            
            db = SessionLocal()
            dataset = db.query(Dataset).filter(Dataset.id == int(dataset_id)).first()
            db.close()
            
            if not dataset:
                return f"Dataset {dataset_id} not found"
            
            df = data_processor.load_dataset(dataset.file_path)
            info = data_processor.get_dataset_info(df)
            
            return json.dumps({
                "dataset_name": dataset.name,
                "shape": info["shape"],
                "columns": info["columns"],
                "dtypes": info["dtypes"],
                "missing_values": info["missing_values"]
            })
            
        except Exception as e:
            return f"Error loading dataset: {str(e)}"
    
    def _analyze_dataset_tool(self, dataset_id: str) -> str:
        """Tool to analyze dataset and get detailed statistics."""
        try:
            from app.core.database import SessionLocal
            from app.models.dataset import Dataset
            
            db = SessionLocal()
            dataset = db.query(Dataset).filter(Dataset.id == int(dataset_id)).first()
            db.close()
            
            if not dataset:
                return f"Dataset {dataset_id} not found"
            
            df = data_processor.load_dataset(dataset.file_path)
            stats = data_processor.get_column_statistics(df)
            
            return json.dumps({
                "dataset_name": dataset.name,
                "column_statistics": stats,
                "data_quality_score": dataset.data_quality_score
            })
            
        except Exception as e:
            return f"Error analyzing dataset: {str(e)}"
    
    def _create_visualization_tool(self, params: str) -> str:
        """Tool to create visualizations."""
        try:
            params_dict = json.loads(params)
            dataset_id = params_dict["dataset_id"]
            chart_type = params_dict["chart_type"]
            columns = params_dict.get("columns", [])
            
            # Load dataset
            from app.core.database import SessionLocal
            from app.models.dataset import Dataset
            
            db = SessionLocal()
            dataset = db.query(Dataset).filter(Dataset.id == dataset_id).first()
            db.close()
            
            if not dataset:
                return f"Dataset {dataset_id} not found"
            
            df = data_processor.load_dataset(dataset.file_path)
            
            # Create visualization
            chart_data = visualization_service.create_chart(df, chart_type, columns)
            
            return json.dumps({
                "chart_type": chart_type,
                "chart_data": chart_data,
                "message": f"Created {chart_type} chart successfully"
            })
            
        except Exception as e:
            return f"Error creating visualization: {str(e)}"
    
    def _train_model_tool(self, params: str) -> str:
        """Tool to train machine learning models."""
        try:
            params_dict = json.loads(params)
            dataset_id = params_dict["dataset_id"]
            model_type = params_dict["model_type"]
            target_column = params_dict["target_column"]
            
            # This would integrate with the ML service
            return json.dumps({
                "message": f"Model training initiated for {model_type}",
                "status": "training_started",
                "dataset_id": dataset_id,
                "target_column": target_column
            })
            
        except Exception as e:
            return f"Error training model: {str(e)}"
    
    def _execute_code_tool(self, code: str) -> str:
        """Tool to execute Python code safely."""
        try:
            # This would use a sandboxed execution environment
            return "Code execution feature coming soon - requires secure sandbox setup"
            
        except Exception as e:
            return f"Error executing code: {str(e)}"
    
    def _clean_data_tool(self, params: str) -> str:
        """Tool to clean dataset."""
        try:
            params_dict = json.loads(params)
            dataset_id = params_dict["dataset_id"]
            cleaning_options = params_dict.get("cleaning_options", {})
            
            return json.dumps({
                "message": "Data cleaning initiated",
                "dataset_id": dataset_id,
                "options": cleaning_options
            })
            
        except Exception as e:
            return f"Error cleaning data: {str(e)}"
    
    def _get_column_info_tool(self, dataset_id: str) -> str:
        """Tool to get column information."""
        try:
            from app.core.database import SessionLocal
            from app.models.dataset import Dataset
            
            db = SessionLocal()
            dataset = db.query(Dataset).filter(Dataset.id == int(dataset_id)).first()
            db.close()
            
            if not dataset:
                return f"Dataset {dataset_id} not found"
            
            return json.dumps({
                "columns_info": dataset.columns_info,
                "rows_count": dataset.rows_count,
                "columns_count": dataset.columns_count
            })
            
        except Exception as e:
            return f"Error getting column info: {str(e)}"


# Global agent instance
data_science_agent = DataScienceAgent()
