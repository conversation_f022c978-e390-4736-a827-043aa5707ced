# Diorite - AI Data Science Platform

A comprehensive, multi-user AI-powered data science platform built with Next.js frontend and FastAPI backend, featuring intelligent data analysis, machine learning, and conversational AI assistance.

## 🌟 Features

### Frontend (Next.js)
- **Modern UI/UX**: Clean, intuitive interface with coral/pink design theme
- **Fixed Sidebar Navigation**: Easy access to all platform features
- **Responsive Design**: Works seamlessly across desktop and mobile devices
- **Real-time Updates**: Live data and chat interactions

### Backend (FastAPI)
- **Multi-user Support**: Secure user authentication and data isolation
- **AI Agent Integration**: LangChain-powered conversational AI assistant
- **Dataset Management**: Upload, process, and analyze CSV/Excel/JSON files
- **Machine Learning**: Automated model training and prediction
- **Data Visualization**: Interactive charts and plots
- **Background Processing**: Celery-based task queue for long-running operations

### AI Capabilities
- **Conversational Interface**: Natural language interaction with data
- **Automated Insights**: AI-generated data analysis and recommendations
- **Code Execution**: Safe Python code execution in sandboxed environment
- **Smart Suggestions**: Context-aware assistance for data science tasks

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Next.js       │    │   FastAPI       │    │   PostgreSQL    │
│   Frontend      │◄──►│   Backend       │◄──►│   Database      │
│   (Port 3000)   │    │   (Port 8000)   │    │   (Port 5432)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐    ┌─────────────────┐
                       │   Redis         │    │   MinIO         │
                       │   Cache/Queue   │    │   File Storage  │
                       │   (Port 6379)   │    │   (Port 9000)   │
                       └─────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │   Celery        │
                       │   Workers       │
                       │   (Background)  │
                       └─────────────────┘
```

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ and npm
- Python 3.11+
- Docker and Docker Compose
- OpenAI API key

### 1. Clone Repository
```bash
git clone <repository-url>
cd diorite
```

### 2. Setup Backend
```bash
cd backend
cp .env.example .env
# Edit .env and add your OPENAI_API_KEY
chmod +x scripts/*.sh
./scripts/start-dev.sh
```

### 3. Setup Frontend
```bash
cd ../
npm install
npm run dev
```

### 4. Access the Platform
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8000/api/v1/docs
- **Celery Monitor**: http://localhost:5555
- **MinIO Console**: http://localhost:9001
